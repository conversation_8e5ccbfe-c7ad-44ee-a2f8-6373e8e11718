// Frontend API Test Script
// Run this with: node test-frontend-api.js

const axios = require('axios');

const BASE_URL = 'http://localhost:8080';
const USER_ID = '056fe13d-e15b-4166-91d3-67833810fc0e';

console.log('=== Frontend API Test ===');
console.log('Base URL:', BASE_URL);
console.log('User ID:', USER_ID);
console.log('');

async function testEndpoint(name, url, method = 'GET', data = null) {
  try {
    console.log(`Testing ${name}:`);
    console.log(`  ${method} ${url}`);
    
    const config = {
      method,
      url,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:5173'
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    console.log(`  ✓ Success (${response.status})`);
    console.log(`  Response:`, JSON.stringify(response.data, null, 2));
    console.log('');
    return response.data;
  } catch (error) {
    console.log(`  ✗ Failed (${error.response?.status || 'No response'})`);
    console.log(`  Error:`, error.message);
    if (error.response?.data) {
      console.log(`  Response:`, JSON.stringify(error.response.data, null, 2));
    }
    console.log('');
    throw error;
  }
}

async function runTests() {
  try {
    // Test 1: Health check
    await testEndpoint(
      'Health Check',
      `${BASE_URL}/api/profiles/health`
    );

    // Test 2: Profile existence check
    await testEndpoint(
      'Profile Existence Check',
      `${BASE_URL}/api/profiles/exists/${USER_ID}`
    );

    // Test 3: Debug vendor endpoint
    await testEndpoint(
      'Debug Vendor Profile',
      `${BASE_URL}/api/profiles/debug/vendor/${USER_ID}`
    );

    // Test 4: Get vendor profile (the failing endpoint)
    try {
      await testEndpoint(
        'Get Vendor Profile (Main)',
        `${BASE_URL}/api/profiles/vendor/${USER_ID}`
      );
    } catch (error) {
      console.log('Main endpoint failed, trying to create profile...');
      
      // Test 5: Create vendor profile
      await testEndpoint(
        'Create Vendor Profile',
        `${BASE_URL}/api/profiles/vendor`,
        'POST',
        {
          userId: USER_ID,
          businessName: 'Test Business Node Script',
          businessEmail: '<EMAIL>',
          businessPhone: '******-NODE',
          businessCategory: 'Technology',
          profileComplete: false
        }
      );

      // Test 6: Get vendor profile again after creation
      await testEndpoint(
        'Get Vendor Profile (After Creation)',
        `${BASE_URL}/api/profiles/vendor/${USER_ID}`
      );
    }

    console.log('=== All tests completed successfully! ===');

  } catch (error) {
    console.error('=== Test suite failed ===');
    console.error('Final error:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('');
      console.error('Connection refused - make sure the backend is running on port 8080');
      console.error('You can start it with: ./mvnw spring-boot:run');
    }
  }
}

// Run the tests
runTests();
