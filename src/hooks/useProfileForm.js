import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { setProfileComplete } from '../redux/Slice/authSlice';
import axios from 'axios';
import dayjs from '../utils/dayjsConfig';
import { message } from 'antd';
import { transformProfileData, transformFormDataForBackend, validateRequiredFields } from '../utils/formTransformers';

export const useProfileForm = ({ user, isCustomer, initialData, editMode = false, onProfileSaved, form }) => {
  const dispatch = useDispatch();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [allFormValues, setAllFormValues] = useState({});
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [logoUrl, setLogoUrl] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const [userFields, setUserFields] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: ''
  });

  // Initialize user fields with auth data
  useEffect(() => {
    if (user && isCustomer) {
      const userData = {
        first_name: user.given_name || '',
        last_name: user.family_name || '',
        email: user.email || '',
        phone: user.phone_number || ''
      };
      
      setUserFields(userData);
    }
  }, [user, isCustomer]);

  // Load profile data
  useEffect(() => {
    const loadProfileData = async () => {
      if (!user?.sub) return;

      try {
        setLoading(true);
        console.log("useProfileForm: Loading profile data, editMode:", editMode, "initialData:", initialData);

        if (editMode && initialData) {
          console.log("useProfileForm: Edit mode with initial data");
          handleInitialData(initialData);
        } else if (initialData) {
          console.log("useProfileForm: Initial data provided");
          handleInitialData(initialData);
        } else {
          console.log("useProfileForm: Fetching profile data from API");
          await fetchProfileData();
        }
        setIsInitialized(true);
      } catch (error) {
        console.error('Error loading profile data:', error);
        dispatch(setProfileComplete(false));
        loadFromLocalStorage();
      } finally {
        setLoading(false);
      }
    };

    loadProfileData();
  }, [user?.sub, isCustomer, initialData, editMode]);

  const handleInitialData = (data) => {
    try {
      console.log("=== HANDLE INITIAL DATA (EDIT MODE) ===");
      console.log("useProfileForm: handleInitialData called with:", data);
      console.log("useProfileForm: Raw backend data keys:", Object.keys(data));
      console.log("useProfileForm: Raw dateOfBirth from backend:", data?.dateOfBirth);
      console.log("useProfileForm: Is customer:", isCustomer);

      const profileData = transformProfileData(data);
      console.log("useProfileForm: transformed profile data:", profileData);
      console.log("useProfileForm: transformed data keys:", Object.keys(profileData));
      console.log("useProfileForm: Transformed dateOfBirth:", profileData?.dateOfBirth);

      // Check if required vendor fields are present after transformation
      if (!isCustomer) {
        console.log("useProfileForm: Vendor field check:");
        console.log("  - business_name:", profileData.business_name);
        console.log("  - business_email:", profileData.business_email);
        console.log("  - business_phone:", profileData.business_phone);
      }

      setAllFormValues(profileData);

      // Use setTimeout to ensure form is ready
      setTimeout(() => {
        if (form) {
          console.log("useProfileForm: Setting form field values with:", profileData);
          console.log("useProfileForm: Form instance available, setting values...");

          // Validate the date before setting
          if (profileData.dateOfBirth) {
            console.log("useProfileForm: dateOfBirth isValid?", profileData.dateOfBirth.isValid ? profileData.dateOfBirth.isValid() : 'No isValid method');
          }

          form.setFieldsValue(profileData);

          // Verify form values were set
          setTimeout(() => {
            const formValues = form.getFieldsValue(true);
            console.log("useProfileForm: Form values after setting:", formValues);
            console.log("useProfileForm: Form field names:", Object.keys(formValues));
          }, 50);
        } else {
          console.error("useProfileForm: Form instance not available!");
        }
      }, 100);

      if (!isCustomer && data.businessLogo) {
        setLogoUrl(data.businessLogo);
      }

      dispatch(setProfileComplete(data.profileComplete || false));
      console.log("=== HANDLE INITIAL DATA COMPLETE ===");
    } catch (error) {
      console.error('Error handling initial data:', error);
      message.error('Error loading profile data');
    }
  };

  const fetchProfileData = async () => {
    try {
      const checkResponse = await axios.get(
        `${import.meta.env.VITE_APP_API_BASE_PROFILE_URL}/exists/${user.sub}`
      );

      if (checkResponse.data.anyProfileExists) {
        const profileEndpoint = isCustomer
          ? `${import.meta.env.VITE_APP_API_BASE_PROFILE_URL}/customer/${user.sub}`
          : `${import.meta.env.VITE_APP_API_BASE_PROFILE_URL}/vendor/${user.sub}`;

        const profileResponse = await axios.get(profileEndpoint);

        if (profileResponse.data) {
          const profileData = transformProfileData(profileResponse.data);
          setAllFormValues(profileData);
          
          if (form) {
            form.setFieldsValue(profileData);
          }

          if (!isCustomer && profileResponse.data.businessLogo) {
            setLogoUrl(profileResponse.data.businessLogo);
          }

          dispatch(setProfileComplete(profileResponse.data.profileComplete || false));
        }
      } else {
        dispatch(setProfileComplete(false));
      }
    } catch (error) {
      console.error('Error fetching profile data:', error);
      dispatch(setProfileComplete(false));
      throw error;
    }
  };

  const loadFromLocalStorage = () => {
    try {
      const savedProfileData = localStorage.getItem('profileData');
      if (savedProfileData) {
        const parsedData = JSON.parse(savedProfileData);
        setAllFormValues(parsedData);
        if (form) {
          form.setFieldsValue(parsedData);
        }
      }
    } catch (error) {
      console.error('Error loading saved profile data:', error);
    }
  };

  const handleSubmit = async (formValues) => {
    if (!form) {
      console.error('Form instance is not available');
      return;
    }

    try {
      setLoading(true);

      console.log('=== FORM SUBMISSION DEBUG ===');
      console.log('Form values passed to handleSubmit:', formValues);
      console.log('Current allFormValues state:', allFormValues);

      const currentFormValues = form.getFieldsValue(true);
      console.log('Form.getFieldsValue(true):', currentFormValues);
      console.log('Form field names found:', Object.keys(currentFormValues));

      console.log('User fields:', userFields);
      console.log('Is customer:', isCustomer);

      // Get all form values including nested fields
      const allValues = {
        ...allFormValues,
        ...form.getFieldsValue(true)
      };

      console.log('Merged form values:', allValues);

      // Merge with user fields
      const mergedValues = {
        ...allValues,
        firstName: userFields.first_name || allValues.firstName,
        lastName: userFields.last_name || allValues.lastName,
        emailAddress: userFields.email || allValues.emailAddress,
        phoneNumber: userFields.phone || allValues.phoneNumber,
        userId: user?.sub,
        profileComplete: true
      };

      console.log('Final merged values before transformation:', mergedValues);

      // Transform the data for backend
      const transformedData = transformFormDataForBackend(mergedValues, isCustomer);

      console.log('Transformed data for backend:', transformedData);

      // Validate required fields
      validateRequiredFields(transformedData, isCustomer);

      console.log('Validation passed, checking if profile exists...');
      const checkResponse = await axios.get(
        `${import.meta.env.VITE_APP_API_BASE_PROFILE_URL}/exists/${user.sub}`
      );

      console.log('Profile exists check response:', checkResponse.data);

      const response = await saveProfileData(checkResponse.data.anyProfileExists, transformedData);

      console.log('Save profile response:', response.data);

      // Save to localStorage
      localStorage.setItem('profileData', JSON.stringify(mergedValues));

      // Update state
      dispatch(setProfileComplete(true));
      setShowSuccessModal(true);

      if (onProfileSaved) {
        onProfileSaved(response.data || transformedData);
      }

      console.log('=== FORM SUBMISSION SUCCESS ===');
    } catch (error) {
      console.error('=== FORM SUBMISSION ERROR ===');
      console.error('Error details:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      handleSubmitError(error);
    } finally {
      setLoading(false);
    }
  };

  const saveProfileData = async (profileExists, data) => {
    const endpoint = isCustomer
      ? `${import.meta.env.VITE_APP_API_BASE_PROFILE_URL}/customer${profileExists ? `/${user.sub}` : ''}`
      : `${import.meta.env.VITE_APP_API_BASE_PROFILE_URL}/vendor${profileExists ? `/${user.sub}` : ''}`;

    const method = profileExists ? 'put' : 'post';

    console.log('=== SAVE PROFILE DATA ===');
    console.log('Profile exists:', profileExists);
    console.log('Method:', method);
    console.log('Endpoint:', endpoint);
    console.log('Data being sent:', JSON.stringify(data, null, 2));
    console.log('Is customer:', isCustomer);

    try {
      const response = await axios[method](endpoint, data, {
        headers: { "Content-Type": "application/json" }
      });

      console.log('Save profile API response:', response.data);
      console.log('Response status:', response.status);

      return response;
    } catch (error) {
      console.error('Save profile API error:', error);
      console.error('Error response data:', error.response?.data);
      console.error('Error response status:', error.response?.status);
      console.error('Error response headers:', error.response?.headers);
      throw error;
    }
  };

  const handleSubmitError = (error) => {
    console.error('Error saving profile data:', error);

    if (error.errorFields) {
      message.error('Please fill in all required fields before submitting.');
    } else if (error.message === 'Missing required personal information fields') {
      message.error('Please complete the Personal Information section before submitting.');
      setCurrentStep(0);
    } else if (error.message === 'Missing required business information fields') {
      message.error('Please complete the Business Information section before submitting.');
      setCurrentStep(0);
    } else if (error.response) {
      const errorMessage = error.response.data?.message || 'Server error occurred';
      message.error(`Failed to save profile: ${errorMessage}`);
    } else {
      message.error('Failed to save profile information. Please check your connection and try again.');
    }
  };

  const resetFormToDefaults = () => {
    setAllFormValues({});
    setLogoUrl(null);
    if (form) {
      form.resetFields();
    }
  };

  return {
    currentStep,
    setCurrentStep,
    loading,
    allFormValues,
    setAllFormValues,
    showSuccessModal,
    setShowSuccessModal,
    logoUrl,
    setLogoUrl,
    userFields,
    handleSubmit,
    resetFormToDefaults,
    isInitialized
  };
}; 