import { UserSwitchOutlined } from '@ant-design/icons';
import { UserOutlined } from '@ant-design/icons';
import { EnvironmentOutlined } from '@ant-design/icons';
import { BankOutlined } from '@ant-design/icons';
import { SettingOutlined } from '@ant-design/icons';
import { FileOutlined } from '@ant-design/icons';
import { ShoppingOutlined } from '@ant-design/icons';

import React, { useState, useEffect } from 'react';
import {
  Form,
  Button,
  message,
  Alert,
  Modal,
  Typography,
  Spin
} from 'antd';
import {
  CheckCircleOutlined,
  EditOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useProfileForm } from '../../hooks/useProfileForm';
import { CustomerInputs } from './CustomerInputs';
import { VendorInputs } from './VendorInput';
import CustomStepper from './CustomStepper';
import { FormFields } from './FormFields';
import { CustomerProfilePreview, VendorProfilePreview } from './ProfilePreview';
import ProfileCard from './ProfileCard';
import './ProfileManagement.css';

const ProfileManagement = ({ initialData = null, editMode = false, onProfileSaved = null }) => {
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const user = useSelector((state) => state.auth.user);
  const profileComplete = useSelector((state) => state.auth.profileComplete);
  const [showProfileCard, setShowProfileCard] = useState(profileComplete && !editMode);
  const [isEditing, setIsEditing] = useState(editMode);
  const roles = ['vendor', 'customer', 'freelancer', 'admin'];
  const userRoles = roles.filter(role => user?.realm_access?.roles.includes(role));
  const isCustomer = userRoles.includes('customer');
  const isVendor = userRoles.includes('vendor');

  const {
    currentStep,
    setCurrentStep,
    loading,
    allFormValues,
    setAllFormValues,
    showSuccessModal,
    setShowSuccessModal,
    logoUrl,
    setLogoUrl,
    userFields,
    handleSubmit,
    resetFormToDefaults,
    isInitialized
  } = useProfileForm({
    user,
    isCustomer,
    initialData,
    editMode,
    onProfileSaved,
    form
  });

  // Initialize form with initial data if available
  useEffect(() => {
    console.log("ProfileManagement: initialData changed:", initialData);
    console.log("ProfileManagement: editMode:", editMode);

    // Don't set form values here - let useProfileForm handle the transformation
    // This prevents raw backend data from being passed directly to the form
  }, [initialData, editMode]);

  const handleEditClick = () => {
    setIsEditing(true);
    setShowProfileCard(false);
    setCurrentStep(0);
    // Don't set form values here - let useProfileForm handle the transformation
    // This prevents raw backend data from being passed directly to the form
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    setShowProfileCard(true);
    setIsEditing(false);
    setCurrentStep(0);
    resetFormToDefaults();
  };

  const customerSteps = [
    {
      title: t('Personal Information'),
      description: t('Basic personal details'),
      icon: <UserOutlined />,
    },
    {
      title: t('Shipping Information'),
      description: t('Shipping address details'),
      icon: <EnvironmentOutlined />,
    },
    {
      title: t('Business Details'),
      description: t('Business preferences'),
      icon: <BankOutlined />,
    },
    {
      title: t('Preferences'),
      description: t('User preferences'),
      icon: <SettingOutlined />,
    },
    {
      title: t('Preview'),
      description: t('Review your information'),
      icon: <UserOutlined />,
    },
  ];
  
  const vendorSteps = [
    {
      title: t('Business Basics'),
      description: t('Company name & Details'),
      icon: <BankOutlined />,
    },
    {
      title: t('Business Address'),
      description: t('Address details'),
      icon: <EnvironmentOutlined />,
    },
    {
      title: t('Business Details'),
      description: t('Business category & info'),
      icon: <UserOutlined />,
    },
    {
      title: t('Product Details'),
      description: t('Product information'),
      icon: <ShoppingOutlined />,
    },
    {
      title: t('Preferences'),
      description: t('Business preferences'),
      icon: <SettingOutlined />,
    },
    {
      title: t('Additional Documents'),
      description: t('Upload supporting documents'),
      icon: <FileOutlined />,
    },
    {
      title: t('Preview'),
      description: t('Review your information'),
      icon: <UserOutlined />,
    },
  ];

  const steps = isCustomer ? customerSteps : vendorSteps;

  const handleNext = () => {
    form.validateFields()
      .then((values) => {
        // Store the current step's values
        const currentValues = form.getFieldsValue(true);
        setAllFormValues(prev => ({
          ...prev,
          ...currentValues
        }));
        setCurrentStep(currentStep + 1);
      })
      .catch((error) => {
        console.log('Validation failed:', error);
      });
  };

  const handlePrevious = () => {
    // Store the current step's values before going back
    const currentValues = form.getFieldsValue(true);
    setAllFormValues(prev => ({
      ...prev,
      ...currentValues
    }));
    setCurrentStep(currentStep - 1);
  };

  const formatFieldValue = (value, type) => {
    if (value === undefined || value === null) {
      return 'Not provided';
    }

    if (value && typeof value === 'object' && value.format && typeof value.format === 'function') {
      try {
        return value.format('YYYY-MM-DD');
      } catch (error) {
        console.error('Error formatting date:', error);
        return String(value);
      }
    }

    if (type === 'checkbox') {
      return value ? 'Yes' : 'No';
    }

    if (type === 'file' && typeof value === 'object') {
      return value.fileName || 'File uploaded';
    }

    return String(value);
  };

  const renderStepContent = () => {
    // Set initial values for the current step
    const currentStepValues = allFormValues;
    if (form && Object.keys(currentStepValues).length > 0) {
      console.log("ProfileManagement: Setting form values in renderStepContent:", currentStepValues);
      console.log("ProfileManagement: dateOfBirth in allFormValues:", currentStepValues.dateOfBirth);

      // Create a safe copy of values with validated dates
      const safeValues = { ...currentStepValues };

      // Validate and fix any date fields
      Object.keys(safeValues).forEach(key => {
        if (key === 'dateOfBirth' && safeValues[key]) {
          console.log("ProfileManagement: Checking dateOfBirth:", safeValues[key]);
          console.log("ProfileManagement: dateOfBirth type:", typeof safeValues[key]);

          // Only keep valid dayjs objects
          if (typeof safeValues[key] === 'object' && safeValues[key].isValid) {
            if (!safeValues[key].isValid()) {
              console.warn("ProfileManagement: Invalid date detected, removing:", safeValues[key]);
              delete safeValues[key];
            } else {
              console.log("ProfileManagement: Valid date found:", safeValues[key]);
            }
          } else {
            console.warn("ProfileManagement: Date object without isValid method, removing:", safeValues[key]);
            delete safeValues[key];
          }
        }
      });

      console.log("ProfileManagement: Setting safe form values:", safeValues);
      form.setFieldsValue(safeValues);
    }

    if (isCustomer) {
      switch (currentStep) {
        case 0:
          return <FormFields inputs={CustomerInputs.personal_information} userFields={userFields} form={form} allFormValues={allFormValues} />;
        case 1:
          return <FormFields inputs={CustomerInputs.shipping_information.primary_address} userFields={userFields} form={form} allFormValues={allFormValues} />;
        case 2:
          return <FormFields inputs={CustomerInputs.business_details} userFields={userFields} form={form} allFormValues={allFormValues} />;
        case 3:
          return <FormFields inputs={CustomerInputs.preferences.communication_preferences} userFields={userFields} form={form} allFormValues={allFormValues} />;
        case 4:
          return (
            <CustomerProfilePreview
              formValues={allFormValues}
              userFields={userFields}
              onEdit={setCurrentStep}
              formatFieldValue={formatFieldValue}
            />
          );
        default:
          return null;
      }
    } else {
      switch (currentStep) {
        case 0:
          return <FormFields inputs={VendorInputs.business_basic_details} userFields={userFields} form={form} allFormValues={allFormValues} />;
        case 1:
          return <FormFields inputs={VendorInputs.business_address} userFields={userFields} form={form} allFormValues={allFormValues} />;
        case 2:
          return <FormFields inputs={VendorInputs.business_details} userFields={userFields} form={form} allFormValues={allFormValues} />;
        case 3:
          return <FormFields inputs={VendorInputs.product_details} userFields={userFields} form={form} allFormValues={allFormValues} />;
        case 4:
          return <FormFields inputs={VendorInputs.preferences} userFields={userFields} form={form} allFormValues={allFormValues} />;
        case 5:
          return <FormFields inputs={VendorInputs.additional_documents} userFields={userFields} form={form} allFormValues={allFormValues} />;
        case 6:
          return (
            <VendorProfilePreview
              formValues={allFormValues}
              userFields={userFields}
              onEdit={setCurrentStep}
              formatFieldValue={formatFieldValue}
              logoUrl={logoUrl}
            />
          );
        default:
          return null;
      }
    }
  };

  if (showProfileCard) {
    return (
      <div className="profile-management-container">
        <ProfileCard onEdit={handleEditClick} />
      </div>
    );
  }

  if (!isInitialized) {
    return (
      <div className="profile-management-container">
        <div className="loading-container">
          <Spin size="large" />
          <p>{t('Loading profile data...')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="profile-management-container">
      <div className="profile-steps-container">
        <CustomStepper 
          steps={steps.map(step => ({
            title: t(step.title),
            description: t(step.description)
          }))}
          currentStep={currentStep}
        />
      </div>

      <div className="profile-form-container">
        {profileComplete && !isEditing && (
          <Alert
            message={t("Profile Complete")}
            description={t("Your profile information has been successfully saved.")}
            type="success"
            showIcon
            icon={<CheckCircleOutlined />}
            style={{ marginBottom: 20 }}
          />
        )}

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={allFormValues}
          className='form-fields'
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          style={{ width: '100%' }}
          preserve={false}
        >
          {renderStepContent()}

          <div className="form-buttons">
            <div className="form-buttons-left">
              {currentStep < steps.length - 1 && (
                <Button className="cancel-button" onClick={() => {
                  setShowProfileCard(true);
                  setIsEditing(false);
                  setCurrentStep(0);
                  form.resetFields();
                }}>
                  {t('Cancel')}
                </Button>
              )}
            </div>

            <div className="form-buttons-right">
              {currentStep > 0 && (
                <Button className="previous-button" onClick={handlePrevious}>
                  {t('Previous')}
                </Button>
              )}

              {currentStep < steps.length - 1 ? (
                <Button className="next-button" onClick={handleNext}>
                  {t('Next')}
                </Button>
              ) : (
                <Button 
                  className="save-button" 
                  onClick={() => form.submit()} 
                  loading={loading}
                >
                  {initialData ? t('Update Profile') : t('Submit Profile')}
                </Button>
              )}
            </div>
          </div>
        </Form>

        <Modal
          open={showSuccessModal}
          onOk={handleSuccessModalClose}
          onCancel={handleSuccessModalClose}
          title={t('Profile Updated')}
          okText={t('View Profile')}
          cancelText={t('Close')}
          centered
        >
          <p>{t('Your profile information has been successfully updated!')}</p>
        </Modal>
      </div>
    </div>
  );
};

export default ProfileManagement;
