import React, { useState, useEffect } from 'react';
import { Modal, Button, Space, Typography } from 'antd';
import {
  DownloadOutlined,
  FileOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FileTextOutlined,
  <PERSON>ZipOutlined,
  FileUnknownOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './FilePreviewModal.css';

const { Text } = Typography;

/**
 * FilePreviewModal component for previewing files before downloading
 * @param {Object} props Component props
 * @param {boolean} props.visible Whether the modal is visible
 * @param {function} props.onClose Callback when the modal is closed
 * @param {string} props.fileUrl URL of the file to preview
 * @param {string} props.downloadUrl URL for downloading the file
 * @param {string} props.fileName Name of the file
 */
const FilePreviewModal = ({ visible, onClose, fileUrl, downloadUrl, fileName }) => {
  const { t } = useTranslation();
  const [fileType, setFileType] = useState('unknown');

  // Determine file type from extension
  useEffect(() => {
    if (!fileName) return;

    const extension = fileName.split('.').pop().toLowerCase();

    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension)) {
      setFileType('image');
    } else if (extension === 'pdf') {
      setFileType('pdf');
    } else if (['doc', 'docx', 'rtf', 'odt'].includes(extension)) {
      setFileType('word');
    } else if (['xls', 'xlsx', 'csv', 'ods'].includes(extension)) {
      setFileType('excel');
    } else if (['txt', 'text', 'json', 'xml', 'html', 'htm', 'md', 'log'].includes(extension)) {
      setFileType('text');
    } else if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(extension)) {
      setFileType('zip');
    } else {
      setFileType('unknown');
    }

  }, [fileName]);

  // Get appropriate icon based on file type
  const getFileIcon = () => {
    switch (fileType) {
      case 'image':
        return <FileImageOutlined />;

      case 'pdf':
        return <FilePdfOutlined />;
      case 'word':
        return <FileWordOutlined />;
      case 'excel':
        return <FileExcelOutlined />;
      case 'text':
        return <FileTextOutlined />;
      case 'zip':
        return <FileZipOutlined />;
      case 'unknown':
        return <FileUnknownOutlined />;
      default:
        return <FileOutlined />;
    }
  };

  // Simplified error handling
  const handleFileError = () => {
    console.error('FilePreviewModal: Error loading file:', fileUrl);
  };

  // Simplified file preview - Chrome-friendly approach
  const renderFilePreview = () => {
    switch (fileType) {
      case 'image':
        return (
          <div className="image-preview-container">
            <img
              src={fileUrl}
              alt={fileName}
              className="preview-image"
              style={{
                maxWidth: '100%',
                maxHeight: '100%'
              }}
              onError={handleFileError}
            />
          </div>
        );
      case 'pdf':
        // PDF preview with fallback options
        return (
          <div className="pdf-preview-container">
            <div className="pdf-viewer-wrapper" style={{ width: '100%', height: '600px', border: '1px solid #d9d9d9' }}>
              <object
                data={`${fileUrl}#toolbar=1&navpanes=1&scrollbar=1`}
                type="application/pdf"
                width="100%"
                height="100%"
                style={{ display: 'block' }}
              >
                <embed
                  src={`${fileUrl}#toolbar=1&navpanes=1&scrollbar=1`}
                  type="application/pdf"
                  width="100%"
                  height="100%"
                />
                {/* Fallback if PDF can't be displayed */}
                <div style={{ padding: '20px', textAlign: 'center' }}>
                  <Space direction="vertical" size="large">
                    {getFileIcon()}
                    <Text>{t('PDF cannot be displayed in this browser')}</Text>
                    <Space>
                      <Button
                        type="primary"
                        icon={<EyeOutlined />}
                        onClick={() => window.open(fileUrl, '_blank', 'noopener,noreferrer')}
                      >
                        {t('Open PDF in New Tab')}
                      </Button>
                      <Button
                        icon={<DownloadOutlined />}
                        onClick={() => {
                          const a = document.createElement('a');
                          a.href = downloadUrl;
                          a.download = fileName;
                          a.rel = 'noopener noreferrer';
                          document.body.appendChild(a);
                          a.click();
                          document.body.removeChild(a);
                        }}
                      >
                        {t('Download PDF')}
                      </Button>
                    </Space>
                  </Space>
                </div>
              </object>
            </div>
          </div>
        );
      default:
        // For all other file types, provide open/download options
        return (
          <div className="file-preview-container">
            <Space direction="vertical" size="large" align="center">
              {getFileIcon()}
              <Text strong>{fileName}</Text>
              <Text type="secondary">{t('File preview not available in browser')}</Text>
              <Space>
                <Button
                  type="primary"
                  icon={<EyeOutlined />}
                  onClick={() => window.open(fileUrl, '_blank', 'noopener,noreferrer')}
                >
                  {t('Open in New Tab')}
                </Button>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={() => {
                    const a = document.createElement('a');
                    a.href = downloadUrl;
                    a.download = fileName;
                    a.rel = 'noopener noreferrer';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                  }}
                >
                  {t('Download File')}
                </Button>
              </Space>
            </Space>
          </div>
        );
    }
  };

  return (
    <Modal
      title={
        <Space>
          {getFileIcon()}
          <span>{fileName || t('File Preview')}</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width="80%"
      centered
      className="file-preview-modal"
      footer={[
        <Button key="close" onClick={onClose}>
          {t('Close')}
        </Button>,
        <Button
          key="download"
          type="primary"
          icon={<DownloadOutlined />}
          onClick={() => {
            // Create a temporary anchor element to download with credentials
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = fileName;
            a.rel = 'noopener noreferrer';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
          }}
        >
          {t('Download')}
        </Button>,
      ]}
    >
      <div className="file-preview-content">
        {renderFilePreview()}
      </div>
    </Modal>
  );
};

export default FilePreviewModal;
