import React, { useState, useEffect } from 'react';
import { Modal, Button, Space, Typography } from 'antd';
import {
  DownloadOutlined,
  FileOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FileTextOutlined,
  <PERSON>ZipOutlined,
  FileUnknownOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './FilePreviewModal.css';

const { Text } = Typography;

/**
 * FilePreviewModal component for previewing files before downloading
 * @param {Object} props Component props
 * @param {boolean} props.visible Whether the modal is visible
 * @param {function} props.onClose Callback when the modal is closed
 * @param {string} props.fileUrl URL of the file to preview
 * @param {string} props.downloadUrl URL for downloading the file
 * @param {string} props.fileName Name of the file
 */
const FilePreviewModal = ({ visible, onClose, fileUrl, downloadUrl, fileName }) => {
  const { t } = useTranslation();
  const [fileType, setFileType] = useState('unknown');

  // Determine file type from extension
  useEffect(() => {
    if (!fileName) return;

    const extension = fileName.split('.').pop().toLowerCase();

    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension)) {
      setFileType('image');
    } else if (extension === 'pdf') {
      setFileType('pdf');
    } else if (['doc', 'docx', 'rtf', 'odt'].includes(extension)) {
      setFileType('word');
    } else if (['xls', 'xlsx', 'csv', 'ods'].includes(extension)) {
      setFileType('excel');
    } else if (['txt', 'text', 'json', 'xml', 'html', 'htm', 'md', 'log'].includes(extension)) {
      setFileType('text');
    } else if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(extension)) {
      setFileType('zip');
    } else {
      setFileType('unknown');
    }

  }, [fileName]);

  // Get appropriate icon based on file type
  const getFileIcon = () => {
    switch (fileType) {
      case 'image':
        return <FileImageOutlined />;

      case 'pdf':
        return <FilePdfOutlined />;
      case 'word':
        return <FileWordOutlined />;
      case 'excel':
        return <FileExcelOutlined />;
      case 'text':
        return <FileTextOutlined />;
      case 'zip':
        return <FileZipOutlined />;
      case 'unknown':
        return <FileUnknownOutlined />;
      default:
        return <FileOutlined />;
    }
  };

  // Simplified error handling
  const handleFileError = () => {
    console.error('FilePreviewModal: Error loading file:', fileUrl);
  };

  // Simplified file preview - Chrome-friendly approach
  const renderFilePreview = () => {
    switch (fileType) {
      case 'image':
        return (
          <div className="image-preview-container" style={{ textAlign: 'center' }}>
            <img
              src={fileUrl}
              alt={fileName}
              className="preview-image"
              style={{
                maxWidth: '100%',
                maxHeight: '70vh',
                objectFit: 'contain'
              }}
              onError={handleFileError}
            />
          </div>
        );
      case 'pdf':
        // PDF preview - direct approach since X-Frame-Options prevents embedding
        return (
          <div className="pdf-preview-container">
            <Space direction="vertical" size="large" align="center" style={{ width: '100%', padding: '40px 20px' }}>
              {getFileIcon()}
              <Text strong style={{ fontSize: '16px' }}>{fileName}</Text>
              <Text type="secondary" style={{ textAlign: 'center', maxWidth: '400px' }}>
                {t('PDF files cannot be previewed directly due to security settings. Please use one of the options below:')}
              </Text>
              <Space size="large">
                <Button
                  type="primary"
                  size="large"
                  icon={<EyeOutlined />}
                  onClick={() => window.open(fileUrl, '_blank', 'noopener,noreferrer')}
                >
                  {t('Open PDF in New Tab')}
                </Button>
                <Button
                  size="large"
                  icon={<DownloadOutlined />}
                  onClick={() => {
                    const a = document.createElement('a');
                    a.href = downloadUrl;
                    a.download = fileName;
                    a.rel = 'noopener noreferrer';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                  }}
                >
                  {t('Download PDF')}
                </Button>
              </Space>
              <Text type="secondary" style={{ fontSize: '12px', textAlign: 'center' }}>
                {t('Opening in a new tab will use your browser\'s built-in PDF viewer')}
              </Text>
            </Space>
          </div>
        );
      default:
        // For all other file types, provide open/download options
        return (
          <div className="file-preview-container">
            <Space direction="vertical" size="large" align="center" style={{ width: '100%', padding: '40px 20px' }}>
              {getFileIcon()}
              <Text strong style={{ fontSize: '16px' }}>{fileName}</Text>
              <Text type="secondary" style={{ textAlign: 'center' }}>
                {t('This file type cannot be previewed directly in the browser')}
              </Text>
              <Space size="large">
                <Button
                  type="primary"
                  size="large"
                  icon={<EyeOutlined />}
                  onClick={() => window.open(fileUrl, '_blank', 'noopener,noreferrer')}
                >
                  {t('Open in New Tab')}
                </Button>
                <Button
                  size="large"
                  icon={<DownloadOutlined />}
                  onClick={() => {
                    const a = document.createElement('a');
                    a.href = downloadUrl;
                    a.download = fileName;
                    a.rel = 'noopener noreferrer';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                  }}
                >
                  {t('Download File')}
                </Button>
              </Space>
            </Space>
          </div>
        );
    }
  };

  return (
    <Modal
      title={
        <Space>
          {getFileIcon()}
          <span>{fileName || t('File Preview')}</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width="80%"
      centered
      className="file-preview-modal"
      footer={[
        <Button key="close" onClick={onClose}>
          {t('Close')}
        </Button>,
        <Button
          key="download"
          type="primary"
          icon={<DownloadOutlined />}
          onClick={() => {
            // Create a temporary anchor element to download with credentials
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = fileName;
            a.rel = 'noopener noreferrer';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
          }}
        >
          {t('Download')}
        </Button>,
      ]}
    >
      <div className="file-preview-content">
        {renderFilePreview()}
      </div>
    </Modal>
  );
};

export default FilePreviewModal;
