import React, { useState } from 'react';
import { Card, Button, Input, Alert, Spin, Typography, Space, Divider } from 'antd';
import { PlayCircleOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

/**
 * API Test Component for debugging backend connectivity
 */
const ApiTest = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState([]);
  const [userId, setUserId] = useState('056fe13d-e15b-4166-91d3-67833810fc0e');

  const addResult = (test, success, data, error = null) => {
    const result = {
      test,
      success,
      data,
      error,
      timestamp: new Date().toLocaleTimeString()
    };
    setResults(prev => [...prev, result]);
  };

  const clearResults = () => {
    setResults([]);
  };

  const testEndpoint = async (name, url, method = 'GET', data = null) => {
    try {
      console.log(`Testing ${name}: ${method} ${url}`);
      
      const config = {
        method,
        url,
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
        }
      };
      
      if (data) {
        config.data = data;
      }
      
      const response = await axios(config);
      addResult(name, true, response.data);
      return response.data;
    } catch (error) {
      const errorData = {
        status: error.response?.status,
        statusText: error.response?.statusText,
        message: error.message,
        data: error.response?.data
      };
      addResult(name, false, null, errorData);
      throw error;
    }
  };

  const runAllTests = async () => {
    setLoading(true);
    clearResults();
    
    const baseUrl = import.meta.env.VITE_APP_API_BASE_PROFILE_URL;
    
    try {
      // Test 1: Health check
      await testEndpoint(
        'Health Check',
        `${baseUrl}/health`
      );

      // Test 2: Profile existence check
      await testEndpoint(
        'Profile Existence Check',
        `${baseUrl}/exists/${userId}`
      );

      // Test 3: Debug vendor endpoint
      await testEndpoint(
        'Debug Vendor Profile',
        `${baseUrl}/debug/vendor/${userId}`
      );

      // Test 4: Get vendor profile (the failing endpoint)
      await testEndpoint(
        'Get Vendor Profile (Main)',
        `${baseUrl}/vendor/${userId}`
      );

      // Test 5: Alternative vendor endpoint
      await testEndpoint(
        'Alternative Vendor Endpoint',
        `http://localhost:8080/api/vendor/user/${userId}`
      );

      // Test 6: Create vendor profile
      await testEndpoint(
        'Create Vendor Profile',
        `${baseUrl}/vendor`,
        'POST',
        {
          userId: userId,
          businessName: 'Test Business API Frontend',
          businessEmail: '<EMAIL>',
          businessPhone: '******-FRONT',
          businessCategory: 'Technology',
          profileComplete: false
        }
      );

      // Test 7: Get vendor profile again after creation
      await testEndpoint(
        'Get Vendor Profile (After Creation)',
        `${baseUrl}/vendor/${userId}`
      );

    } catch (error) {
      console.error('Test suite error:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderResult = (result, index) => {
    const { test, success, data, error, timestamp } = result;
    
    return (
      <Card 
        key={index}
        size="small" 
        style={{ marginBottom: 8 }}
        title={
          <Space>
            {success ? 
              <CheckCircleOutlined style={{ color: '#52c41a' }} /> : 
              <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
            }
            <Text strong>{test}</Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>{timestamp}</Text>
          </Space>
        }
      >
        {success ? (
          <div>
            <Text type="success">✓ Success</Text>
            <Divider type="vertical" />
            <Text>Status: 200</Text>
            <br />
            <TextArea
              value={JSON.stringify(data, null, 2)}
              rows={4}
              style={{ marginTop: 8, fontSize: '12px' }}
              readOnly
            />
          </div>
        ) : (
          <div>
            <Text type="danger">✗ Failed</Text>
            <Divider type="vertical" />
            <Text>Status: {error?.status || 'Unknown'}</Text>
            <br />
            <Alert
              message={error?.message || 'Unknown error'}
              description={
                <TextArea
                  value={JSON.stringify(error, null, 2)}
                  rows={3}
                  style={{ fontSize: '12px' }}
                  readOnly
                />
              }
              type="error"
              size="small"
              style={{ marginTop: 8 }}
            />
          </div>
        )}
      </Card>
    );
  };

  return (
    <div style={{ padding: 20 }}>
      <Title level={3}>API Connectivity Test</Title>
      <Paragraph>
        This tool helps debug the vendor profile API connectivity issues.
        It tests various endpoints to identify where the problem occurs.
      </Paragraph>
      
      <Card style={{ marginBottom: 20 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>User ID to test:</Text>
            <Input
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              placeholder="Enter user ID"
              style={{ marginTop: 8 }}
            />
          </div>
          
          <div>
            <Text strong>API Base URL:</Text>
            <Text code style={{ marginLeft: 8 }}>
              {import.meta.env.VITE_APP_API_BASE_PROFILE_URL}
            </Text>
          </div>
          
          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={runAllTests}
              loading={loading}
              disabled={!userId}
            >
              Run All Tests
            </Button>
            <Button onClick={clearResults}>
              Clear Results
            </Button>
          </Space>
        </Space>
      </Card>

      {loading && (
        <div style={{ textAlign: 'center', padding: 20 }}>
          <Spin size="large" tip="Running API tests..." />
        </div>
      )}

      {results.length > 0 && (
        <div>
          <Title level={4}>Test Results ({results.length})</Title>
          {results.map(renderResult)}
        </div>
      )}
    </div>
  );
};

export default ApiTest;
