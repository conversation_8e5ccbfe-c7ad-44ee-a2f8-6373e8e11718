import React, { useState } from 'react';
import { Card, Button, Input, Alert, Typography, Space, Divider } from 'antd';
import { PlayCircleOutlined } from '@ant-design/icons';
import { transformFormDataForBackend, validateRequiredFields, transformProfileData } from '../../utils/formTransformers';

const { Title, Text } = Typography;
const { TextArea } = Input;

/**
 * Form Data Transformation Test Component
 */
const FormDataTest = () => {
  const [testData, setTestData] = useState({
    // Using the actual field names from getFieldName function
    business_name: 'Test Business Inc',
    business_email: '<EMAIL>',
    business_phone: '******-0123',
    street_address: '123 Business St',
    city: 'Business City',
    state: 'CA',
    zip_code: '12345',
    country: 'USA',
    business_category: 'Technology',
    business_subcategory: 'Software',
    business_website: 'https://testbusiness.com',
    years_of_experience: '5',
    product_name: 'Test Product',
    product_description: 'A test product description',
    product_category: 'SaaS',
    registration_number: 'REG123456',
    regions_supported: 'North America',
    license_details: 'Standard Business License',
    userId: '056fe13d-e15b-4166-91d3-67833810fc0e',
    profileComplete: true
  });

  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  const [backendData, setBackendData] = useState({
    userId: '056fe13d-e15b-4166-91d3-67833810fc0e',
    businessName: 'Test Business Inc',
    businessEmail: '<EMAIL>',
    businessPhone: '******-0123',
    streetAddress: '123 Business St',
    city: 'Business City',
    state: 'CA',
    zipCode: '12345',
    country: 'USA',
    businessCategory: 'Technology',
    businessSubcategory: 'Software',
    businessWebsite: 'https://testbusiness.com',
    yearsOfExperience: '5',
    productName: 'Test Product',
    productDescription: 'A test product description',
    productCategory: 'SaaS',
    registrationNumber: 'REG123456',
    regionsSupported: 'North America',
    licenseDetails: 'Standard Business License',
    profileComplete: true
  });
  const [backendToFormResult, setBackendToFormResult] = useState(null);

  const testTransformation = () => {
    try {
      console.log('Testing transformation with data:', testData);

      // Test vendor transformation (isCustomer = false)
      const transformedData = transformFormDataForBackend(testData, false);

      console.log('Transformed data:', transformedData);

      // Test validation
      validateRequiredFields(transformedData, false);

      setResult(transformedData);
      setError(null);
    } catch (err) {
      console.error('Transformation error:', err);
      setError(err.message);
      setResult(null);
    }
  };

  const testBackendToForm = () => {
    try {
      console.log('Testing backend to form transformation with data:', backendData);

      // Test backend to form transformation
      const formData = transformProfileData(backendData);

      console.log('Backend to form data:', formData);

      setBackendToFormResult(formData);
    } catch (err) {
      console.error('Backend to form transformation error:', err);
      setBackendToFormResult({ error: err.message });
    }
  };

  const updateTestData = (newData) => {
    try {
      const parsed = JSON.parse(newData);
      setTestData(parsed);
    } catch (err) {
      console.error('Invalid JSON:', err);
    }
  };

  return (
    <div style={{ padding: 20 }}>
      <Title level={3}>Form Data Transformation Test</Title>
      
      <Card title="Input Data (Form Values)" style={{ marginBottom: 20 }}>
        <TextArea
          value={JSON.stringify(testData, null, 2)}
          onChange={(e) => updateTestData(e.target.value)}
          rows={15}
          style={{ fontFamily: 'monospace', fontSize: '12px' }}
        />
        <div style={{ marginTop: 10 }}>
          <Space>
            <Button type="primary" icon={<PlayCircleOutlined />} onClick={testTransformation}>
              Test Form → Backend
            </Button>
            <Button type="default" icon={<PlayCircleOutlined />} onClick={testBackendToForm}>
              Test Backend → Form (Edit Mode)
            </Button>
          </Space>
        </div>
      </Card>

      <Card title="Backend Data (Edit Mode Test)" style={{ marginBottom: 20 }}>
        <TextArea
          value={JSON.stringify(backendData, null, 2)}
          onChange={(e) => {
            try {
              setBackendData(JSON.parse(e.target.value));
            } catch (err) {
              console.error('Invalid JSON:', err);
            }
          }}
          rows={10}
          style={{ fontFamily: 'monospace', fontSize: '12px' }}
        />
      </Card>

      {backendToFormResult && (
        <Card title="Backend → Form Transformation Result" style={{ marginBottom: 20 }}>
          <TextArea
            value={JSON.stringify(backendToFormResult, null, 2)}
            rows={10}
            readOnly
            style={{ fontFamily: 'monospace', fontSize: '12px' }}
          />
          <Divider />
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text strong>Edit Mode Field Mapping:</Text>
            <Text>Backend 'businessName' → Form 'business_name': {backendToFormResult.business_name}</Text>
            <Text>Backend 'businessEmail' → Form 'business_email': {backendToFormResult.business_email}</Text>
            <Text>Backend 'streetAddress' → Form 'street_address': {backendToFormResult.street_address}</Text>
          </Space>
        </Card>
      )}

      {error && (
        <Alert
          message="Transformation Error"
          description={error}
          type="error"
          style={{ marginBottom: 20 }}
        />
      )}

      {result && (
        <Card title="Transformed Data (Backend Format)" style={{ marginBottom: 20 }}>
          <TextArea
            value={JSON.stringify(result, null, 2)}
            rows={15}
            readOnly
            style={{ fontFamily: 'monospace', fontSize: '12px' }}
          />
          <Divider />
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text strong>Validation Results:</Text>
            <Text type="success">✓ Required fields validation passed</Text>
            <Text>✓ Business Name: {result.businessName}</Text>
            <Text>✓ Business Email: {result.businessEmail}</Text>
            <Text>✓ User ID: {result.userId}</Text>
            <Divider />
            <Text strong>Address Fields Check:</Text>
            <Text type={result.streetAddress ? "success" : "danger"}>
              Street Address: {result.streetAddress || 'MISSING'}
            </Text>
            <Text type={result.city ? "success" : "danger"}>
              City: {result.city || 'MISSING'}
            </Text>
            <Text type={result.state ? "success" : "danger"}>
              State: {result.state || 'MISSING'}
            </Text>
            <Text type={result.zipCode ? "success" : "danger"}>
              Zip Code: {result.zipCode || 'MISSING'}
            </Text>
            <Text type={result.country ? "success" : "danger"}>
              Country: {result.country || 'MISSING'}
            </Text>
            <Divider />
            <Text strong>Field Mapping Check:</Text>
            <Text>Form field 'business_name' → Backend field 'businessName'</Text>
            <Text>Form field 'business_email' → Backend field 'businessEmail'</Text>
            <Text>Form field 'street_address' → Backend field 'streetAddress'</Text>
            <Text>Form field 'city' → Backend field 'city'</Text>
            <Text>Form field 'zip_code' → Backend field 'zipCode'</Text>
          </Space>
        </Card>
      )}

      <Card title="Expected Backend API Call" style={{ marginBottom: 20 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text strong>Endpoint:</Text>
          <Text code>POST http://localhost:8080/api/profiles/vendor</Text>
          
          <Text strong>Headers:</Text>
          <Text code>Content-Type: application/json</Text>
          
          <Text strong>Body:</Text>
          <Text>The transformed data shown above</Text>
        </Space>
      </Card>
    </div>
  );
};

export default FormDataTest;
