import React, { useState } from 'react';
import { Card, Button, Input, Alert, Typography, Space, Divider } from 'antd';
import { PlayCircleOutlined } from '@ant-design/icons';
import { transformFormDataForBackend, validateRequiredFields } from '../../utils/formTransformers';

const { Title, Text } = Typography;
const { TextArea } = Input;

/**
 * Form Data Transformation Test Component
 */
const FormDataTest = () => {
  const [testData, setTestData] = useState({
    'Business Name': 'Test Business Inc',
    'Business Email': '<EMAIL>',
    'Business Phone': '******-0123',
    'Street Address': '123 Business St',
    'City': 'Business City',
    'State': 'CA',
    'Zip Code': '12345',
    'Country': 'USA',
    'Business Category': 'Technology',
    'Business Subcategory': 'Software',
    'Business Website': 'https://testbusiness.com',
    'Years of Experience': '5',
    'Product Name': 'Test Product',
    'Product Description': 'A test product description',
    'Product Category': 'SaaS',
    'Registration Number': 'REG123456',
    'Regions Supported': 'North America',
    'License Details': 'Standard Business License',
    userId: '056fe13d-e15b-4166-91d3-67833810fc0e',
    profileComplete: true
  });

  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  const testTransformation = () => {
    try {
      console.log('Testing transformation with data:', testData);
      
      // Test vendor transformation (isCustomer = false)
      const transformedData = transformFormDataForBackend(testData, false);
      
      console.log('Transformed data:', transformedData);
      
      // Test validation
      validateRequiredFields(transformedData, false);
      
      setResult(transformedData);
      setError(null);
    } catch (err) {
      console.error('Transformation error:', err);
      setError(err.message);
      setResult(null);
    }
  };

  const updateTestData = (newData) => {
    try {
      const parsed = JSON.parse(newData);
      setTestData(parsed);
    } catch (err) {
      console.error('Invalid JSON:', err);
    }
  };

  return (
    <div style={{ padding: 20 }}>
      <Title level={3}>Form Data Transformation Test</Title>
      
      <Card title="Input Data (Form Values)" style={{ marginBottom: 20 }}>
        <TextArea
          value={JSON.stringify(testData, null, 2)}
          onChange={(e) => updateTestData(e.target.value)}
          rows={15}
          style={{ fontFamily: 'monospace', fontSize: '12px' }}
        />
        <div style={{ marginTop: 10 }}>
          <Button type="primary" icon={<PlayCircleOutlined />} onClick={testTransformation}>
            Test Transformation
          </Button>
        </div>
      </Card>

      {error && (
        <Alert
          message="Transformation Error"
          description={error}
          type="error"
          style={{ marginBottom: 20 }}
        />
      )}

      {result && (
        <Card title="Transformed Data (Backend Format)" style={{ marginBottom: 20 }}>
          <TextArea
            value={JSON.stringify(result, null, 2)}
            rows={15}
            readOnly
            style={{ fontFamily: 'monospace', fontSize: '12px' }}
          />
          <Divider />
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text strong>Validation Results:</Text>
            <Text type="success">✓ Required fields validation passed</Text>
            <Text>✓ Business Name: {result.businessName}</Text>
            <Text>✓ Business Email: {result.businessEmail}</Text>
            <Text>✓ User ID: {result.userId}</Text>
          </Space>
        </Card>
      )}

      <Card title="Expected Backend API Call" style={{ marginBottom: 20 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text strong>Endpoint:</Text>
          <Text code>POST http://localhost:8080/api/profiles/vendor</Text>
          
          <Text strong>Headers:</Text>
          <Text code>Content-Type: application/json</Text>
          
          <Text strong>Body:</Text>
          <Text>The transformed data shown above</Text>
        </Space>
      </Card>
    </div>
  );
};

export default FormDataTest;
