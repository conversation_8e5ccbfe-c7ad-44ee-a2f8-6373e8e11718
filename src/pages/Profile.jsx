import React, { useState, useEffect } from 'react';
import { Layout, theme, Tabs, Spin, Alert } from 'antd';
import { useSelector } from 'react-redux';
import axios from 'axios';
import CustomSider from '../components/Pages/CustomSider';
import ProfileManagement from '../components/Profile/ProfileManagement';
import ProfileCard from '../components/Profile/ProfileCard';
import PageHeader from '../components/Common/PageHeader';
import ApiTest from '../components/Debug/ApiTest';
import FormDataTest from '../components/Debug/FormDataTest';
import { UserOutlined, SettingOutlined, EditOutlined, BugOutlined, ExperimentOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Header, Content, Footer } = Layout;

const Profile = () => {
  const { t } = useTranslation();
  const [collapsed, setCollapsed] = useState(false);
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const user = useSelector((state) => state.auth.user);
 
  const [profileData, setProfileData] = useState(null);
  const [isProfileComplete, setIsProfileComplete] = useState(false);
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);

  const accessRoles = user?.realm_access?.roles || [];
  const roles = ['vendor', 'customer', 'freelancer'];
  const userRoles = roles.filter(role => accessRoles.includes(role));
  const isCustomer = userRoles.includes('customer');
  const isVendor = userRoles.includes('vendor');

  useEffect(() => {
    const fetchProfileData = async () => {
      if (!user?.sub) return;

      console.log("Fetching profile data for user:", user.sub);
      console.log("User roles:", userRoles);
      console.log("Is customer:", isCustomer);
      console.log("Is vendor:", isVendor);

      setLoading(true);
      try {
        // First, check if any profile exists
        const existsEndpoint = `${import.meta.env.VITE_APP_API_BASE_PROFILE_URL}/exists/${user.sub}`;
        console.log("Checking profile existence at:", existsEndpoint);

        const existsResponse = await axios.get(existsEndpoint);
        console.log("Profile existence check:", existsResponse.data);

        // Determine which endpoint to use based on user role
        const profileEndpoint = isCustomer
          ? `${import.meta.env.VITE_APP_API_BASE_PROFILE_URL}/customer/${user.sub}`
          : `${import.meta.env.VITE_APP_API_BASE_PROFILE_URL}/vendor/${user.sub}`;

        console.log("Fetching profile from:", profileEndpoint);

        const response = await axios.get(profileEndpoint);
        console.log("Profile data fetched successfully:", response.data);

        setProfileData(response.data);
        setIsProfileComplete(response.data?.profileComplete || false);
      } catch (error) {
        console.error('Error fetching profile data:', error);
        console.error('Error response:', error.response?.data);
        console.error('Error status:', error.response?.status);
        console.error('Error config:', error.config);

        // If we get a 404, it means the profile doesn't exist yet
        if (error.response && error.response.status === 404) {
          console.log("Profile not found (404) - this is normal for new users");
          setIsProfileComplete(false);
          setProfileData(null);
        } else {
          const errorMessage = error.response?.data?.message || error.message || 'Unknown error';
          console.error('Setting error message:', errorMessage);
          setError(t('Failed to load profile data') + ': ' + errorMessage);
        }
      } finally {
        setLoading(false);
      }
    };

    // Always try to fetch profile data if user exists
    // This ensures we have data available for both view and edit modes
    if (user?.sub) {
      fetchProfileData();
    } else {
      setLoading(false);
    }
  }, [user?.sub, isCustomer, isVendor, t]);

  const handleEditProfile = () => {
    console.log("Edit profile clicked, current profileData:", profileData);
    console.log("Raw profileData dateOfBirth:", profileData?.dateOfBirth);
    setEditMode(true);
  };

  const handleProfileSaved = (savedProfile) => {
    setProfileData(savedProfile);
    setIsProfileComplete(true);
    setEditMode(false);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <CustomSider collapsed={collapsed} setCollapsed={setCollapsed} />
      <Layout style={{
        padding: '0',
        width: '100%',
        maxWidth: 'auto',
        margin: '0 auto',
        marginLeft: collapsed ? '80px' : '220px',
        transition: 'margin-left 0.3s'
      }}>


            <PageHeader
              title={t('Profile Management')}
              timestamp="Last login: April 12, 2023 - 5:07 PM"
              showSearch={true}
              onSearch={(value) => console.log('Search:', value)}
              userAvatar="https://xsgames.co/randomusers/avatar.php?g=pixel"
            />

        <Content style={{ margin: '0 16px' }}>
          <div
            style={{
              minHeight: 360,
              background: colorBgContainer,
              borderRadius: borderRadiusLG,
            }}
          >
            {loading ? (
              <div style={{ display: 'flex', justifyContent: 'center', padding: '50px' }}>
                <Spin size="large" tip={t('Loading profile data...')} />
              </div>
            ) : error ? (
              <div style={{ padding: '20px' }}>
                <Alert
                  message={t('Error Loading Profile')}
                  description={
                    <div>
                      <p>{error}</p>
                      <p style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
                        <strong>Troubleshooting:</strong><br />
                        1. Make sure the backend service is running on port 8080<br />
                        2. Check if PostgreSQL is running on port 7010<br />
                        3. Try the API Debug tab below to test connectivity
                      </p>
                    </div>
                  }
                  type="error"
                  showIcon
                  style={{ marginBottom: '20px' }}
                />
                <Tabs defaultActiveKey="api-debug" size="large">
                  <Tabs.TabPane
                    tab={<span><BugOutlined />{t('API Debug')}</span>}
                    key="api-debug"
                  >
                    <ApiTest />
                  </Tabs.TabPane>
                  <Tabs.TabPane
                    tab={<span><ExperimentOutlined />{t('Form Test')}</span>}
                    key="form-test"
                  >
                    <FormDataTest />
                  </Tabs.TabPane>
                </Tabs>
              </div>
            ) : !isProfileComplete || editMode ? (
              <Tabs defaultActiveKey="profile-setup" size="large" style={{ padding: '20px' }}>
                <Tabs.TabPane
                  tab={<span><UserOutlined />{t('Profile Setup')}</span>}
                  key="profile-setup"
                >

                  <ProfileManagement
                    initialData={profileData}
                    editMode={editMode}
                    onProfileSaved={handleProfileSaved}
                  />
                </Tabs.TabPane>
                <Tabs.TabPane
                  tab={<span><BugOutlined />{t('API Debug')}</span>}
                  key="api-debug"
                >
                  <ApiTest />
                </Tabs.TabPane>
                <Tabs.TabPane
                  tab={<span><ExperimentOutlined />{t('Form Test')}</span>}
                  key="form-test"
                >
                  <FormDataTest />
                </Tabs.TabPane>
              </Tabs>
            ) : (
              <div style={{ padding: '20px' }}>
                <ProfileCard
                  profileData={profileData}
                  isCustomer={isCustomer}
                  onEdit={handleEditProfile}
                />
              </div>
            )}
          </div>
        </Content>
        <Footer style={{ textAlign: 'center' }}>
          {/* Ant Design ©{new Date().getFullYear()} Created by Ant UED */}
        </Footer>
      </Layout>
    </Layout>
  );
};

export default Profile;